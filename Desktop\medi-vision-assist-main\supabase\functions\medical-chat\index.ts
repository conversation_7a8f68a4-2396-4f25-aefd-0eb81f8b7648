
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface ChatRequest {
  question: string
  medicineName?: string
  userId?: string
}

const OPENROUTER_API_KEY = "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"

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { question, medicineName, userId }: ChatRequest = await req.json()
    
    if (!question) {
      throw new Error('Question is required')
    }

    console.log(`🤖 Processing medical chat request`)
    
    const startTime = Date.now()
    
    // Create the prompt for GPT
    const systemPrompt = `You are a knowledgeable medical assistant with extensive training on over 100 medical topics. You provide accurate, helpful information about medicines, supplements, health conditions, and general medical guidance based on established medical knowledge.

IMPORTANT GUIDELINES:
- Always base your answers on established medical knowledge and evidence-based medicine
- Never provide specific medical advice - always recommend consulting healthcare providers for personalized medical decisions
- Be clear about limitations and when professional consultation is needed
- Focus on general safety information, drug interactions, side effects, and well-known medical facts
- If unsure about something, clearly state the limitation and suggest consulting a healthcare professional
- You can answer questions about general health, nutrition, exercise, preventive care, and wellness
- For medication questions, provide general information about common uses, dosages, and precautions
- Always emphasize the importance of professional medical consultation for specific medical decisions

${medicineName && medicineName !== 'General Medical Assistant' ? `Context: User is asking about ${medicineName}` : ''}

User question: ${question}

Provide a helpful, accurate response while emphasizing the importance of professional medical consultation for specific medical decisions.`

    console.log('🔑 Making request to OpenRouter API...')
    
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://ygkxdctaraeragizxfbt.supabase.co',
        'X-Title': 'MediVision Assist'
      },
      body: JSON.stringify({
        model: 'openai/gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: question
          }
        ],
        max_tokens: 500,
        temperature: 0.3
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('OpenRouter API error:', errorText)
      throw new Error(`OpenRouter API error: ${response.status}`)
    }

    const chatResponse = await response.json()
    const reply = chatResponse.choices?.[0]?.message?.content

    if (!reply) {
      throw new Error('No response from AI model')
    }

    const responseTime = Date.now() - startTime
    console.log(`✅ Chat response generated in ${responseTime}ms`)

    // Store the chat session if userId provided
    let sessionId = null
    if (userId) {
      const supabase = createClient(
        Deno.env.get('SUPABASE_URL') ?? '',
        Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
      )

      const { data: session, error } = await supabase
        .from('chat_sessions')
        .insert({
          user_id: userId,
          medicine_name: medicineName || 'General Medical Query',
          question: question,
          response: reply,
          response_time_ms: responseTime
        })
        .select('id')
        .single()

      if (error) {
        console.error('Failed to store chat session:', error)
      } else {
        sessionId = session?.id
      }
    }

    return new Response(
      JSON.stringify({
        success: true,
        reply,
        sessionId,
        responseTime
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200 
      }
    )

  } catch (error) {
    console.error('❌ Medical chat error:', error)
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'Failed to process chat request',
        reply: "I apologize, but I'm having trouble processing your request right now. Please try again later or consult with a healthcare professional for medical questions."
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500 
      }
    )
  }
})
